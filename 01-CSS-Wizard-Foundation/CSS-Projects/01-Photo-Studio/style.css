@layer reset {
    /**
     * Universal box-sizing reset
     * Ensures all elements use border-box sizing for predictable layouts
     */
    *,
    *::before,
    *::after {
        box-sizing: border-box;
    }

    /**
     * Prevent text size adjustments on mobile devices
     * Maintains consistent typography across different devices
     * Reference: https://kilianvalkhof.com/2022/css-html/your-css-reset-needs-text-size-adjust-probably/
     */
    html {
        -moz-text-size-adjust: none;
        -webkit-text-size-adjust: none;
        text-size-adjust: none;
        background-color: var(--clr-white);
    }

    /**
     * Remove default margins from common elements
     * Creates a clean slate for custom spacing
     */
    body,
    h1,
    h2,
    h3,
    h4,
    p,
    figure,
    blockquote,
    dl,
    dd {
        margin: 0;
    }

    /**
     * Reset list styles for elements with list role
     * Ensures accessibility while removing default styling
     * Reference: https://www.scottohara.me/blog/2019/01/12/lists-and-safari.html
     */
    [role="list"] {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    /**
     * Base body styles
     * Sets minimum height and readable line spacing
     */
    body {
        min-block-size: 100vh;  /* Ensures body takes full viewport height */
        line-height: 1.6; /* Improves text readability */
    }

    /**
     * Tighter line height for headings and form elements
     * Improves visual hierarchy and form appearance
     */
    h1,
    h2,
    h3,
    button,
    input,
    label {
        line-height: 1.1;
    }

    /**
     * Modern text wrapping for better typography
     * - balance: Prevents orphaned words in headings
     * - pretty: Improves line breaks in body text
     */
    h1,
    h2,
    h3,
    h4 {
        text-wrap: balance;
    }

    p,
    li {
        text-wrap: pretty;
    }

    /**
     * Responsive images and pictures
     * Prevents overflow and maintains aspect ratios
     */
    img,
    picture {
        max-inline-size: 100%;  /* Responsive width */
        display: block;         /* Removes inline spacing issues */
    }

    /**
     * Form elements inherit font styles
     * Ensures consistent typography across all form controls
     */
    input,
    button,
    textarea,
    select {
        font: inherit;
    }
}

@layer base {
    :root {
        /*colors*/
        --clr-white: #EEEEEE;
        --clr-gray: #595959;
        --clr-black: #2E2E2E;

        /*Typography*/
        /* Font Family*/
        --ff-heading: "Inter", sans-serif;
        --ff-body: "Inter", sans-serif;
        --ff-list: "Inter", sans-serif;

        /* Font Weight*/
        --fw-heading: Bold;
        --fw-body: Regular;
        --fw-list: Medium;

        /*Font Sizes*/
        --fs-heading: clamp(1rem, 0.895rem + 0.6723vw, 1.5rem);
        --fs-body: clamp(0.875rem, 0.8487rem + 0.1681vw, 1rem);
        --fs-list: clamp(0.875rem, 0.8487rem + 0.1681vw, 1rem);

        /*Font Height*/
        --lh-heading: clamp(1.5rem, 1.395rem + 0.6723vw, 2rem);
        --lh-body: clamp(1.375rem, 1.3487rem + 0.1681vw, 1.5rem);
        --lh-list: clamp(1.375rem, 1.3487rem + 0.1681vw, 1.5rem);

        /*Letter Spacing*/
        --ls-heading: -0.96px;

    }
}

@layer layout {
    main{
        padding: clamp(1rem, 0.6849rem + 2.0168vw, 2.5rem);
        display: grid;
        grid-template-columns: 1fr 2fr 2fr;
        gap: 1.5rem;

        &:nth-child(1){
            grid-column: 1/2;
        }
        &:nth-child(2){
            grid-column: 2/3;
        }
        &:nth-child(3){
            grid-column: 3/4;
        }

        @media (max-width: 1024px) {
            grid-template-columns: 1fr 2fr;
        }
        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }
}

@layer utilities {

    .title{
        font-family: var(--ff-heading), sans-serif;
        font-weight: var(--fw-heading);
        font-size: var(--fs-heading);
        line-height: var(--lh-heading);
        letter-spacing: var(--ls-heading);
        color: var(--clr-black);
        text-transform: uppercase;
    }

    .body-copy{
        font-family: var(--ff-body), sans-serif;
        font-weight: var(--fw-body);
        font-size: var(--fs-body);
        line-height: var(--lh-body);
        color: var(--clr-gray);
    }

    .list{
        font-family: var(--ff-list), sans-serif;
        font-weight: var(--fw-list);
        font-size: var(--fs-list);
        line-height: var(--lh-list);
        color: var(--clr-gray);
        text-transform: capitalize;
    }
}